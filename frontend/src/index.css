@tailwind base;
@tailwind components;
@tailwind utilities;

/* 项目卡片优化样式 */
@layer components {
  .project-card-description {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.6;
  }

  /* 头部渐变背景（保留原有样式） */
  .project-card-gradient {
    background: linear-gradient(135deg,
      #667eea 0%,
      #764ba2 25%,
      #f093fb 50%,
      #f5576c 75%,
      #4facfe 100%
    );
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
  }

  /* 全卡片渐变背景 */
  .project-card-full-gradient {
    background: linear-gradient(135deg,
      #667eea 0%,
      #764ba2 25%,
      #f093fb 50%,
      #f5576c 75%,
      #4facfe 100%
    );
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
    position: relative;
  }

  /* 统一的深色半透明覆盖层 */
  .project-card-unified-overlay {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(8px);
    border-radius: 0.75rem;
    margin: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    min-height: 200px;
    display: flex;
    flex-direction: column;
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .project-card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .project-card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* 在深色覆盖层上的文字样式优化 */
  .project-card-text-unified {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  }

  /* 项目标题样式 */
  .project-card-title {
    color: rgba(255, 255, 255, 1);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  }

  /* 项目描述区域 */
  .project-card-description-area {
    flex: 1;
    min-height: 60px;
    display: flex;
    align-items: flex-start;
  }

  /* 项目信息区域 */
  .project-card-info-area {
    margin-top: auto;
    padding-top: 1rem;
  }
}
